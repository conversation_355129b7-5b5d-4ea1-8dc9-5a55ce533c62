import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SupabaseService } from '../../../core/services/supabase.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  HostPlantSpecies,
  SymptomSeverity,
  BCTVSymptomType,
  ObservationType,
  GeoLocation
} from '../../../core/models/observation.model';
import { PhotoUploadComponent } from '../../../shared/components/photo-upload/photo-upload.component';
import { LocationPickerComponent } from '../../../shared/components/location-picker/location-picker.component';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-bctv-symptoms-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PhotoUploadComponent,
    LocationPickerComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="form-container">
      <div class="form-header">
        <h1>BCTV Symptoms Observation</h1>
        <p>Record BCTV symptom observations on host plants</p>
        <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
      </div>

      <form [formGroup]="bctvForm" (ngSubmit)="onSubmit()" class="observation-form">
        <!-- Location Section -->
        <div class="form-section">
          <h3>📍 Location Information</h3>
          <app-location-picker
            [required]="true"
            (locationSelected)="onLocationSelected($event)"
            (locationCleared)="onLocationCleared()">
          </app-location-picker>
        </div>

        <!-- BCTV Symptoms Details -->
        <div class="form-section">
          <h3>🦠 BCTV Symptoms Details</h3>

          <div class="form-row">
            <div class="form-group">
              <label for="hostPlantSpecies">Affected Host Plant Species *</label>
              <select
                id="hostPlantSpecies"
                formControlName="hostPlantSpecies"
                class="form-control"
                [class.error]="isFieldInvalid('hostPlantSpecies')">
                <option value="">Select host plant species</option>
                <option value="russian_thistle">Russian Thistle (Salsola tragus)</option>
                <option value="kochia">Kochia (Bassia scoparia)</option>
                <option value="lambsquarters">Lambsquarters (Chenopodium album)</option>
                <option value="pigweed">Pigweed (Amaranthus spp.)</option>
                <option value="shepherds_purse">Shepherd's Purse (Capsella bursa-pastoris)</option>
                <option value="london_rocket">London Rocket (Sisymbrium irio)</option>
                <option value="prickly_lettuce">Prickly Lettuce (Lactuca serriola)</option>
                <option value="mustard">Mustard (Brassica spp.)</option>
                <option value="filaree">Filaree (Erodium cicutarium)</option>
                <option value="malva">Malva (Malva spp.)</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('hostPlantSpecies')">
                Host plant species is required
              </div>
            </div>

            <div class="form-group">
              <label for="symptomSeverity">Symptom Severity *</label>
              <select
                id="symptomSeverity"
                formControlName="symptomSeverity"
                class="form-control"
                [class.error]="isFieldInvalid('symptomSeverity')">
                <option value="">Select severity level</option>
                <option value="none">None - No visible symptoms</option>
                <option value="mild">Mild - Slight symptoms, minimal impact</option>
                <option value="moderate">Moderate - Noticeable symptoms, some impact</option>
                <option value="severe">Severe - Pronounced symptoms, significant impact</option>
                <option value="very_severe">Very Severe - Extreme symptoms, plant severely affected</option>
              </select>
              <div class="error-message" *ngIf="isFieldInvalid('symptomSeverity')">
                Symptom severity is required
              </div>
            </div>
          </div>

          <div class="form-group">
            <label>Observed Symptom Types *</label>
            <div class="checkbox-group">
              <div class="checkbox-item" *ngFor="let symptom of symptomTypes">
                <input
                  type="checkbox"
                  [id]="'symptom-' + symptom.value"
                  [value]="symptom.value"
                  (change)="onSymptomTypeChange($event)">
                <label [for]="'symptom-' + symptom.value">{{symptom.label}}</label>
              </div>
            </div>
            <div class="error-message" *ngIf="selectedSymptomTypes.length === 0 && bctvForm.get('symptomTypes')?.touched">
              At least one symptom type must be selected
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="affectedPlantCount">Number of Affected Plants</label>
              <input
                id="affectedPlantCount"
                type="number"
                formControlName="affectedPlantCount"
                class="form-control"
                placeholder="Count of plants showing symptoms"
                min="0">
            </div>

            <div class="form-group">
              <label for="totalPlantCount">Total Plants in Area</label>
              <input
                id="totalPlantCount"
                type="number"
                formControlName="totalPlantCount"
                class="form-control"
                placeholder="Total plant count in observed area"
                min="0">
            </div>
          </div>

          <div class="form-group">
            <label for="symptomDescription">Detailed Symptom Description</label>
            <textarea
              id="symptomDescription"
              formControlName="symptomDescription"
              class="form-control"
              rows="4"
              placeholder="Describe the symptoms in detail, including distribution patterns, progression, and any other relevant observations...">
            </textarea>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="form-section">
          <h3>📝 Additional Information</h3>

          <div class="form-group">
            <label for="notes">General Notes</label>
            <textarea
              id="notes"
              formControlName="notes"
              class="form-control"
              rows="4"
              placeholder="Environmental conditions, weather, other observations, etc.">
            </textarea>
          </div>
        </div>

        <!-- Photo Upload -->
        <div class="form-section">
          <h3>📷 Photos</h3>
          <p class="section-description">Upload photos showing BCTV symptoms on the affected plants</p>
          <app-photo-upload
            #photoUpload
            [maxFiles]="8"
            [bucket]="'bctv-symptoms-observations'"
            (photosChanged)="onPhotosChanged($event)">
          </app-photo-upload>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="goBack()">
            Cancel
          </button>

          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="bctvForm.invalid || isSubmitting || !selectedLocation || selectedSymptomTypes.length === 0">
            <span *ngIf="!isSubmitting">Save Observation</span>
            <app-loading-spinner *ngIf="isSubmitting" size="small"></app-loading-spinner>
          </button>
        </div>

        <div class="error-message" *ngIf="submitError">
          {{submitError}}
        </div>

        <div class="success-message" *ngIf="submitSuccess">
          BCTV symptoms observation saved successfully!
        </div>
      </form>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background: #f8f9fa;
      min-height: 100vh;
      box-sizing: border-box;
      width: 100%;
      overflow-x: hidden;
    }

    .form-header {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-header h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .form-header p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 1rem;
    }

    .back-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .back-btn:hover {
      background: #545b62;
    }

    .observation-form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .form-section {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-section h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      border-bottom: 2px solid #dc3545;
      padding-bottom: 0.5rem;
    }

    .section-description {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 0.875rem;
      font-style: italic;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-control:focus {
      outline: none;
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    .form-control.error {
      border-color: #dc3545;
    }

    .form-control.error:focus {
      border-color: #dc3545;
      box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 0.75rem;
      margin-top: 0.5rem;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .checkbox-item input[type="checkbox"] {
      width: 16px;
      height: 16px;
      accent-color: #dc3545;
    }

    .checkbox-item label {
      margin: 0;
      cursor: pointer;
      font-size: 0.875rem;
      color: #333;
    }

    .error-message {
      color: #dc3545;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .success-message {
      color: #28a745;
      font-size: 0.875rem;
      margin-top: 1rem;
      padding: 0.75rem;
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #dc3545;
      color: white;
    }

    .btn-primary:hover:not(:disabled) {
      background: #c82333;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      .form-container {
        padding: 1rem;
        margin: 0;
        max-width: 100%;
      }

      .form-header,
      .form-section,
      .form-actions {
        padding: 1.5rem;
        margin-left: 0;
        margin-right: 0;
      }

      .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .checkbox-group {
        grid-template-columns: 1fr;
      }

      .form-actions {
        flex-direction: column;
      }

      .form-control {
        width: 100%;
        box-sizing: border-box;
      }
    }

    @media (max-width: 480px) {
      .form-container {
        padding: 0.5rem;
      }

      .form-header,
      .form-section,
      .form-actions {
        padding: 1rem;
      }

      .form-header h1 {
        font-size: 1.5rem;
      }
    }
  `]
})
export class BCTVSymptomsFormComponent implements OnInit {
  @ViewChild('photoUpload') photoUpload!: PhotoUploadComponent;

  bctvForm: FormGroup;
  selectedLocation: GeoLocation | null = null;
  selectedPhotos: File[] = [];
  selectedSymptomTypes: BCTVSymptomType[] = [];
  isSubmitting = false;
  submitError = '';
  submitSuccess = false;

  symptomTypes = [
    { value: BCTVSymptomType.LEAF_CURLING, label: 'Leaf Curling' },
    { value: BCTVSymptomType.YELLOWING, label: 'Yellowing' },
    { value: BCTVSymptomType.STUNTING, label: 'Stunting' },
    { value: BCTVSymptomType.VEIN_CLEARING, label: 'Vein Clearing' },
    { value: BCTVSymptomType.NECROSIS, label: 'Necrosis' },
    { value: BCTVSymptomType.WILTING, label: 'Wilting' }
  ];

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private authService: AuthService,
    private router: Router
  ) {
    this.bctvForm = this.fb.group({
      hostPlantSpecies: ['', Validators.required],
      symptomSeverity: ['', Validators.required],
      affectedPlantCount: [''],
      totalPlantCount: [''],
      symptomDescription: [''],
      notes: ['']
    });
  }

  ngOnInit() {}

  onLocationSelected(location: GeoLocation) {
    this.selectedLocation = location;
  }

  onLocationCleared() {
    this.selectedLocation = null;
  }

  onPhotosChanged(photos: File[]) {
    this.selectedPhotos = photos;
  }

  onSymptomTypeChange(event: Event) {
    const checkbox = event.target as HTMLInputElement;
    const value = checkbox.value as BCTVSymptomType;

    if (checkbox.checked) {
      if (!this.selectedSymptomTypes.includes(value)) {
        this.selectedSymptomTypes.push(value);
      }
    } else {
      this.selectedSymptomTypes = this.selectedSymptomTypes.filter(type => type !== value);
    }

    // Mark the symptomTypes field as touched for validation
    this.bctvForm.get('symptomTypes')?.markAsTouched();
  }

  async onSubmit() {
    if (this.bctvForm.valid && this.selectedLocation && this.selectedSymptomTypes.length > 0) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitSuccess = false;

      try {
        const currentUser = this.authService.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Upload photos first if any
        let photoUrls: string[] = [];
        if (this.selectedPhotos.length > 0) {
          photoUrls = await this.photoUpload.uploadPhotos();
        }

        const formValue = this.bctvForm.value;

        // Create observation record with full user information
        const observation = {
          user_id: currentUser.id,
          user_name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.email,
          user_email: currentUser.email,
          type: ObservationType.BCTV_SYMPTOMS,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          accuracy: this.selectedLocation.accuracy,
          address: this.selectedLocation.address,
          bctv_data: {
            hostPlantSpecies: formValue.hostPlantSpecies,
            symptomSeverity: formValue.symptomSeverity,
            symptomTypes: this.selectedSymptomTypes,
            affectedPlantCount: formValue.affectedPlantCount ? parseInt(formValue.affectedPlantCount) : null,
            totalPlantCount: formValue.totalPlantCount ? parseInt(formValue.totalPlantCount) : null,
            symptomDescription: formValue.symptomDescription
          },
          photos: photoUrls,
          notes: formValue.notes,
          timestamp: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data: insertedData, error } = await this.supabaseService.db
          .from('observations')
          .insert(observation)
          .select('id')
          .single();

        if (error) throw error;

        this.submitSuccess = true;

        // Navigate to dashboard and focus on the new observation
        setTimeout(() => {
          if (insertedData?.id) {
            // Import and use MapNavigationService
            import('../../../core/services/map-navigation.service').then(({ MapNavigationService }) => {
              const mapNavService = new MapNavigationService(this.router);
              mapNavService.navigateToNewObservation(
                this.selectedLocation.latitude,
                this.selectedLocation.longitude,
                insertedData.id,
                'bctv_symptoms'
              );
            });
          } else {
            this.router.navigate(['/dashboard']);
          }
        }, 2000);

      } catch (error) {
        console.error('Error saving observation:', error);
        this.submitError = 'Failed to save observation. Please try again.';
      } finally {
        this.isSubmitting = false;
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.bctvForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched() {
    Object.keys(this.bctvForm.controls).forEach(key => {
      const control = this.bctvForm.get(key);
      control?.markAsTouched();
    });

    // Also mark symptom types as touched
    this.bctvForm.get('symptomTypes')?.markAsTouched();
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
