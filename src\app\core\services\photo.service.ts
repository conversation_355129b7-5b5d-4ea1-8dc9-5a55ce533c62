import { Injectable } from '@angular/core';
import { SupabaseService } from './supabase.service';

@Injectable({
  providedIn: 'root'
})
export class PhotoService {
  constructor(private supabaseService: SupabaseService) {}

  /**
   * Get public URL for a photo stored in Supabase Storage
   */
  getPhotoUrl(photoPath: string): string {
    if (!photoPath) return '';
    
    try {
      // Extract bucket name from path (e.g., "observations/filename.jpg" -> bucket: "observations")
      const pathParts = photoPath.split('/');
      const bucket = pathParts[0];
      const filePath = pathParts.slice(1).join('/');
      
      const { data } = this.supabaseService.storage
        .from(bucket)
        .getPublicUrl(filePath);
      
      return data.publicUrl;
    } catch (error) {
      console.error('Error getting photo URL:', error);
      return '';
    }
  }

  /**
   * Get multiple photo URLs
   */
  getPhotoUrls(photoPaths: string[]): string[] {
    if (!photoPaths || !Array.isArray(photoPaths)) return [];
    
    return photoPaths
      .map(path => this.getPhotoUrl(path))
      .filter(url => url !== '');
  }

  /**
   * Generate thumbnail URL with size parameters
   */
  getThumbnailUrl(photoPath: string, width: number = 200, height: number = 200): string {
    const baseUrl = this.getPhotoUrl(photoPath);
    if (!baseUrl) return '';
    
    // Add transformation parameters for thumbnail generation
    // Note: This depends on your Supabase configuration and image transformation setup
    return `${baseUrl}?width=${width}&height=${height}&resize=cover`;
  }

  /**
   * Check if a photo exists and is accessible
   */
  async checkPhotoExists(photoPath: string): Promise<boolean> {
    if (!photoPath) return false;
    
    try {
      const pathParts = photoPath.split('/');
      const bucket = pathParts[0];
      const filePath = pathParts.slice(1).join('/');
      
      const { data, error } = await this.supabaseService.storage
        .from(bucket)
        .list(filePath.split('/').slice(0, -1).join('/'), {
          search: filePath.split('/').pop()
        });
      
      return !error && data && data.length > 0;
    } catch (error) {
      console.error('Error checking photo existence:', error);
      return false;
    }
  }

  /**
   * Delete a photo from storage
   */
  async deletePhoto(photoPath: string): Promise<boolean> {
    if (!photoPath) return false;
    
    try {
      const pathParts = photoPath.split('/');
      const bucket = pathParts[0];
      const filePath = pathParts.slice(1).join('/');
      
      const { error } = await this.supabaseService.storage
        .from(bucket)
        .remove([filePath]);
      
      if (error) {
        console.error('Error deleting photo:', error);
        return false;
      }
      
      return true;
    } catch (error) {
      console.error('Error deleting photo:', error);
      return false;
    }
  }

  /**
   * Upload a single photo and return the path
   */
  async uploadPhoto(file: File, bucket: string = 'observations'): Promise<string> {
    try {
      const fileName = `${Date.now()}_${file.name}`;
      const filePath = `${bucket}/${fileName}`;

      const { data, error } = await this.supabaseService.storage
        .from(bucket)
        .upload(filePath, file);

      if (error) throw error;

      return filePath;
    } catch (error) {
      console.error('Error uploading photo:', error);
      throw error;
    }
  }

  /**
   * Upload multiple photos and return their paths
   */
  async uploadPhotos(files: File[], bucket: string = 'observations'): Promise<string[]> {
    try {
      const uploadPromises = files.map(file => this.uploadPhoto(file, bucket));
      return await Promise.all(uploadPromises);
    } catch (error) {
      console.error('Error uploading photos:', error);
      throw error;
    }
  }

  /**
   * Validate image file
   */
  validateImageFile(file: File, maxSize: number = 10 * 1024 * 1024): { valid: boolean; error?: string } {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return { valid: false, error: 'File must be an image' };
    }

    // Check file size
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      return { valid: false, error: `File size must be less than ${maxSizeMB}MB` };
    }

    // Check for supported formats
    const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!supportedFormats.includes(file.type)) {
      return { valid: false, error: 'Supported formats: JPEG, PNG, WebP' };
    }

    return { valid: true };
  }

  /**
   * Format file size for display
   */
  formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Create a preview URL for a file
   */
  createPreviewUrl(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target?.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * Compress image before upload (optional)
   */
  async compressImage(file: File, maxWidth: number = 1920, quality: number = 0.8): Promise<File> {
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        // Calculate new dimensions
        const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
        canvas.width = img.width * ratio;
        canvas.height = img.height * ratio;
        
        // Draw and compress
        ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            resolve(file); // Return original if compression fails
          }
        }, file.type, quality);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }
}
