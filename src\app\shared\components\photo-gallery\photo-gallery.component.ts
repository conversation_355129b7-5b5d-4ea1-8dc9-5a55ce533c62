import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PhotoService } from '../../../core/services/photo.service';

@Component({
  selector: 'app-photo-gallery',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="photo-gallery" *ngIf="photoUrls.length > 0">
      <div class="gallery-header" *ngIf="showHeader">
        <h3>📷 Photos ({{photoUrls.length}})</h3>
      </div>
      
      <div class="gallery-grid" [class]="gridClass">
        <div class="photo-item" 
             *ngFor="let photoUrl of photoUrls; let i = index"
             (click)="openLightbox(i)">
          <img 
            [src]="photoUrl" 
            [alt]="'Photo ' + (i + 1)"
            class="photo-thumbnail"
            (error)="onImageError($event, i)"
            loading="lazy">
          <div class="photo-overlay">
            <div class="zoom-icon">🔍</div>
          </div>
        </div>
      </div>

      <!-- Lightbox Modal -->
      <div class="lightbox-overlay" 
           *ngIf="lightboxOpen" 
           (click)="closeLightbox()"
           [@fadeInOut]>
        <div class="lightbox-container" (click)="$event.stopPropagation()">
          <button class="lightbox-close" (click)="closeLightbox()">×</button>
          
          <div class="lightbox-content">
            <img 
              [src]="photoUrls[currentPhotoIndex]" 
              [alt]="'Photo ' + (currentPhotoIndex + 1)"
              class="lightbox-image">
          </div>
          
          <div class="lightbox-controls" *ngIf="photoUrls.length > 1">
            <button class="nav-btn prev" 
                    (click)="previousPhoto()" 
                    [disabled]="currentPhotoIndex === 0">
              ‹
            </button>
            <span class="photo-counter">
              {{currentPhotoIndex + 1}} / {{photoUrls.length}}
            </span>
            <button class="nav-btn next" 
                    (click)="nextPhoto()" 
                    [disabled]="currentPhotoIndex === photoUrls.length - 1">
              ›
            </button>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .photo-gallery {
      width: 100%;
    }

    .gallery-header h3 {
      margin: 0 0 1rem 0;
      color: #333;
      font-size: 1.125rem;
      font-weight: 600;
    }

    .gallery-grid {
      display: grid;
      gap: 1rem;
    }

    .gallery-grid.small {
      grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    }

    .gallery-grid.medium {
      grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .gallery-grid.large {
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }

    .photo-item {
      position: relative;
      aspect-ratio: 1;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .photo-item:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    }

    .photo-thumbnail {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    .photo-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      display: flex;
      align-items: center;
      justify-content: center;
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    .photo-item:hover .photo-overlay {
      opacity: 1;
    }

    .zoom-icon {
      color: white;
      font-size: 1.5rem;
    }

    /* Lightbox Styles */
    .lightbox-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.9);
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10000;
      padding: 2rem;
    }

    .lightbox-container {
      position: relative;
      max-width: 90vw;
      max-height: 90vh;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .lightbox-close {
      position: absolute;
      top: -3rem;
      right: 0;
      background: none;
      border: none;
      color: white;
      font-size: 2rem;
      cursor: pointer;
      z-index: 10001;
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background-color 0.2s ease;
    }

    .lightbox-close:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .lightbox-content {
      display: flex;
      align-items: center;
      justify-content: center;
      max-width: 100%;
      max-height: calc(90vh - 4rem);
    }

    .lightbox-image {
      max-width: 100%;
      max-height: 100%;
      object-fit: contain;
      border-radius: 8px;
    }

    .lightbox-controls {
      display: flex;
      align-items: center;
      gap: 1rem;
      margin-top: 1rem;
      color: white;
    }

    .nav-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      font-size: 1.5rem;
      width: 40px;
      height: 40px;
      border-radius: 50%;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s ease;
    }

    .nav-btn:hover:not(:disabled) {
      background: rgba(255, 255, 255, 0.3);
    }

    .nav-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .photo-counter {
      font-size: 0.875rem;
      color: rgba(255, 255, 255, 0.8);
      min-width: 60px;
      text-align: center;
    }

    /* Error state */
    .photo-item.error {
      background: #f8f9fa;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #6c757d;
    }

    .photo-item.error::before {
      content: '🖼️';
      font-size: 2rem;
    }

    /* Mobile optimizations */
    @media (max-width: 768px) {
      .gallery-grid.small {
        grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
      }

      .gallery-grid.medium {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
      }

      .gallery-grid.large {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
      }

      .lightbox-overlay {
        padding: 1rem;
      }

      .lightbox-close {
        top: -2.5rem;
        font-size: 1.5rem;
        width: 35px;
        height: 35px;
      }

      .nav-btn {
        width: 35px;
        height: 35px;
        font-size: 1.25rem;
      }
    }

    /* Animation */
    @keyframes fadeInOut {
      from { opacity: 0; }
      to { opacity: 1; }
    }
  `],
  animations: []
})
export class PhotoGalleryComponent implements OnInit {
  @Input() photoPaths: string[] = [];
  @Input() size: 'small' | 'medium' | 'large' = 'medium';
  @Input() showHeader: boolean = true;

  photoUrls: string[] = [];
  lightboxOpen = false;
  currentPhotoIndex = 0;

  constructor(private photoService: PhotoService) {}

  ngOnInit() {
    this.loadPhotoUrls();
  }

  ngOnChanges() {
    this.loadPhotoUrls();
  }

  get gridClass(): string {
    return this.size;
  }

  private loadPhotoUrls() {
    if (this.photoPaths && this.photoPaths.length > 0) {
      this.photoUrls = this.photoService.getPhotoUrls(this.photoPaths);
    } else {
      this.photoUrls = [];
    }
  }

  openLightbox(index: number) {
    this.currentPhotoIndex = index;
    this.lightboxOpen = true;
    document.body.style.overflow = 'hidden'; // Prevent background scrolling
  }

  closeLightbox() {
    this.lightboxOpen = false;
    document.body.style.overflow = ''; // Restore scrolling
  }

  previousPhoto() {
    if (this.currentPhotoIndex > 0) {
      this.currentPhotoIndex--;
    }
  }

  nextPhoto() {
    if (this.currentPhotoIndex < this.photoUrls.length - 1) {
      this.currentPhotoIndex++;
    }
  }

  onImageError(event: Event, index: number) {
    const img = event.target as HTMLImageElement;
    const photoItem = img.closest('.photo-item');
    if (photoItem) {
      photoItem.classList.add('error');
      img.style.display = 'none';
    }
    console.error(`Failed to load image at index ${index}:`, this.photoPaths[index]);
  }
}
