import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { SupabaseService } from '../../../core/services/supabase.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  BLHDensity,
  BLHBehavior,
  ObservationType,
  GeoLocation
} from '../../../core/models/observation.model';
import { PhotoUploadComponent } from '../../../shared/components/photo-upload/photo-upload.component';
import { LocationPickerComponent } from '../../../shared/components/location-picker/location-picker.component';
import { LoadingSpinnerComponent } from '../../../shared/components/loading-spinner/loading-spinner.component';

@Component({
  selector: 'app-blh-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    PhotoUploadComponent,
    LocationPickerComponent,
    LoadingSpinnerComponent
  ],
  template: `
    <div class="form-container">
      <div class="form-header">
        <h1>BLH Observation</h1>
        <p>Record Beet Leafhopper population and behavior</p>
        <button (click)="goBack()" class="back-btn">← Back to Dashboard</button>
      </div>

      <form [formGroup]="blhForm" (ngSubmit)="onSubmit()" class="observation-form">
        <!-- Location Section -->
        <div class="form-section">
          <h3>📍 Location Information</h3>
          <app-location-picker
            [required]="true"
            (locationSelected)="onLocationSelected($event)"
            (locationCleared)="onLocationCleared()">
          </app-location-picker>
        </div>

        <!-- BLH Details -->
        <div class="form-section">
          <h3>🦗 BLH Population Details</h3>

          <div class="form-row">
            <div class="form-group">
              <label for="adultCount">Adult Count</label>
              <input
                id="adultCount"
                type="number"
                formControlName="adultCount"
                class="form-control"
                placeholder="Number of adult BLH observed"
                min="0">
            </div>

            <div class="form-group">
              <label for="nymphCount">Nymph Count</label>
              <input
                id="nymphCount"
                type="number"
                formControlName="nymphCount"
                class="form-control"
                placeholder="Number of nymphs observed"
                min="0">
            </div>
          </div>

          <div class="form-group">
            <label for="density">Population Density *</label>
            <select
              id="density"
              formControlName="density"
              class="form-control"
              [class.error]="isFieldInvalid('density')">
              <option value="">Select density</option>
              <option value="none">None (0 per plant)</option>
              <option value="low">Low (1-5 per plant)</option>
              <option value="medium">Medium (6-15 per plant)</option>
              <option value="high">High (16-30 per plant)</option>
              <option value="very_high">Very High (> 30 per plant)</option>
            </select>
            <div class="error-message" *ngIf="isFieldInvalid('density')">
              Population density is required
            </div>
          </div>

          <div class="form-group">
            <label>Observed Behaviors</label>
            <div class="checkbox-group">
              <label class="checkbox-item" *ngFor="let behavior of behaviorOptions">
                <input
                  type="checkbox"
                  [value]="behavior.value"
                  (change)="onBehaviorChange(behavior.value, $event)">
                <span>{{behavior.label}}</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Weather Conditions -->
        <div class="form-section">
          <h3>🌤️ Weather Conditions</h3>

          <div class="form-row">
            <div class="form-group">
              <label for="temperature">Temperature (°C)</label>
              <input
                id="temperature"
                type="number"
                formControlName="temperature"
                class="form-control"
                placeholder="Current temperature"
                step="0.1">
            </div>

            <div class="form-group">
              <label for="humidity">Humidity (%)</label>
              <input
                id="humidity"
                type="number"
                formControlName="humidity"
                class="form-control"
                placeholder="Relative humidity"
                min="0"
                max="100">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label for="windSpeed">Wind Speed (km/h)</label>
              <input
                id="windSpeed"
                type="number"
                formControlName="windSpeed"
                class="form-control"
                placeholder="Wind speed"
                min="0"
                step="0.1">
            </div>

            <div class="form-group">
              <label for="windDirection">Wind Direction</label>
              <select
                id="windDirection"
                formControlName="windDirection"
                class="form-control">
                <option value="">Select direction</option>
                <option value="N">North</option>
                <option value="NE">Northeast</option>
                <option value="E">East</option>
                <option value="SE">Southeast</option>
                <option value="S">South</option>
                <option value="SW">Southwest</option>
                <option value="W">West</option>
                <option value="NW">Northwest</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Additional Information -->
        <div class="form-section">
          <h3>📝 Additional Information</h3>

          <div class="form-group">
            <label for="notes">Notes</label>
            <textarea
              id="notes"
              formControlName="notes"
              class="form-control"
              rows="4"
              placeholder="Additional observations, host plants present, etc.">
            </textarea>
          </div>
        </div>

        <!-- Photo Upload -->
        <div class="form-section">
          <h3>📷 Photos</h3>
          <app-photo-upload
            #photoUpload
            [maxFiles]="5"
            [bucket]="'blh-observations'"
            (photosChanged)="onPhotosChanged($event)">
          </app-photo-upload>
        </div>

        <!-- Form Actions -->
        <div class="form-actions">
          <button
            type="button"
            class="btn btn-secondary"
            (click)="goBack()">
            Cancel
          </button>

          <button
            type="submit"
            class="btn btn-primary"
            [disabled]="blhForm.invalid || isSubmitting || !selectedLocation">
            <span *ngIf="!isSubmitting">Save Observation</span>
            <app-loading-spinner *ngIf="isSubmitting" size="small"></app-loading-spinner>
          </button>
        </div>

        <div class="error-message" *ngIf="submitError">
          {{submitError}}
        </div>

        <div class="success-message" *ngIf="submitSuccess">
          BLH observation saved successfully!
        </div>
      </form>
    </div>
  `,
  styles: [`
    .form-container {
      max-width: 800px;
      margin: 0 auto;
      padding: 2rem;
      background: #f8f9fa;
      min-height: 100vh;
    }

    .form-header {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      margin-bottom: 2rem;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-header h1 {
      margin: 0 0 0.5rem 0;
      color: #333;
      font-size: 1.75rem;
      font-weight: 700;
    }

    .form-header p {
      margin: 0 0 1rem 0;
      color: #666;
      font-size: 1rem;
    }

    .back-btn {
      background: #6c757d;
      color: white;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 4px;
      cursor: pointer;
      font-size: 0.875rem;
      transition: background-color 0.2s;
    }

    .back-btn:hover {
      background: #545b62;
    }

    .observation-form {
      display: flex;
      flex-direction: column;
      gap: 2rem;
    }

    .form-section {
      background: white;
      padding: 2rem;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .form-section h3 {
      margin: 0 0 1.5rem 0;
      color: #333;
      font-size: 1.25rem;
      font-weight: 600;
      border-bottom: 2px solid #ffc107;
      padding-bottom: 0.5rem;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1.5rem;
      margin-bottom: 1.5rem;
    }

    .form-group {
      display: flex;
      flex-direction: column;
    }

    .form-group label {
      font-weight: 500;
      color: #333;
      margin-bottom: 0.5rem;
      font-size: 0.875rem;
    }

    .form-control {
      padding: 0.75rem;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 0.875rem;
      transition: border-color 0.2s, box-shadow 0.2s;
    }

    .form-control:focus {
      outline: none;
      border-color: #ffc107;
      box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
    }

    .form-control.error {
      border-color: #dc3545;
    }

    .checkbox-group {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 0.75rem;
      margin-top: 0.5rem;
    }

    .checkbox-item {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      cursor: pointer;
      font-size: 0.875rem;
    }

    .checkbox-item input[type="checkbox"] {
      margin: 0;
    }

    textarea.form-control {
      resize: vertical;
      min-height: 100px;
    }

    .error-message {
      color: #dc3545;
      font-size: 0.75rem;
      margin-top: 0.25rem;
    }

    .success-message {
      color: #28a745;
      font-size: 0.875rem;
      margin-top: 1rem;
      padding: 0.75rem;
      background: #d4edda;
      border: 1px solid #c3e6cb;
      border-radius: 4px;
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      padding: 2rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 4px;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .btn-primary {
      background: #ffc107;
      color: #333;
    }

    .btn-primary:hover:not(:disabled) {
      background: #e0a800;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-secondary:hover {
      background: #545b62;
    }

    .btn:disabled {
      background: #6c757d;
      cursor: not-allowed;
    }

    @media (max-width: 768px) {
      .form-container {
        padding: 1rem;
      }

      .form-header,
      .form-section,
      .form-actions {
        padding: 1.5rem;
      }

      .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
      }

      .checkbox-group {
        grid-template-columns: 1fr;
      }

      .form-actions {
        flex-direction: column;
      }
    }
  `]
})
export class BLHFormComponent implements OnInit {
  @ViewChild('photoUpload') photoUpload!: PhotoUploadComponent;

  blhForm: FormGroup;
  selectedLocation: GeoLocation | null = null;
  selectedPhotos: File[] = [];
  selectedBehaviors: BLHBehavior[] = [];
  isSubmitting = false;
  submitError = '';
  submitSuccess = false;

  behaviorOptions = [
    { value: BLHBehavior.FEEDING, label: 'Feeding' },
    { value: BLHBehavior.MATING, label: 'Mating' },
    { value: BLHBehavior.OVIPOSITING, label: 'Ovipositing' },
    { value: BLHBehavior.DISPERSING, label: 'Dispersing' },
    { value: BLHBehavior.RESTING, label: 'Resting' }
  ];

  constructor(
    private fb: FormBuilder,
    private supabaseService: SupabaseService,
    private authService: AuthService,
    private router: Router
  ) {
    this.blhForm = this.fb.group({
      adultCount: [''],
      nymphCount: [''],
      density: ['', Validators.required],
      temperature: [''],
      humidity: [''],
      windSpeed: [''],
      windDirection: [''],
      notes: ['']
    });
  }

  ngOnInit() {}

  onLocationSelected(location: GeoLocation) {
    this.selectedLocation = location;
  }

  onLocationCleared() {
    this.selectedLocation = null;
  }

  onPhotosChanged(photos: File[]) {
    this.selectedPhotos = photos;
  }

  onBehaviorChange(behavior: BLHBehavior, event: any) {
    if (event.target.checked) {
      this.selectedBehaviors.push(behavior);
    } else {
      const index = this.selectedBehaviors.indexOf(behavior);
      if (index > -1) {
        this.selectedBehaviors.splice(index, 1);
      }
    }
  }

  async onSubmit() {
    if (this.blhForm.valid && this.selectedLocation) {
      this.isSubmitting = true;
      this.submitError = '';
      this.submitSuccess = false;

      try {
        const currentUser = this.authService.currentUser;
        if (!currentUser) {
          throw new Error('User not authenticated');
        }

        // Upload photos first if any
        let photoUrls: string[] = [];
        if (this.selectedPhotos.length > 0) {
          photoUrls = await this.photoUpload.uploadPhotos();
        }

        const formValue = this.blhForm.value;

        // Create observation record with full user information
        const observation = {
          user_id: currentUser.id,
          user_name: `${currentUser.firstName || ''} ${currentUser.lastName || ''}`.trim() || currentUser.email,
          user_email: currentUser.email,
          type: ObservationType.BLH_OBSERVATION,
          latitude: this.selectedLocation.latitude,
          longitude: this.selectedLocation.longitude,
          accuracy: this.selectedLocation.accuracy,
          address: this.selectedLocation.address,
          blh_data: {
            adultCount: formValue.adultCount ? parseInt(formValue.adultCount) : null,
            nymphCount: formValue.nymphCount ? parseInt(formValue.nymphCount) : null,
            density: formValue.density,
            behavior: this.selectedBehaviors,
            weatherConditions: {
              temperature: formValue.temperature ? parseFloat(formValue.temperature) : null,
              humidity: formValue.humidity ? parseFloat(formValue.humidity) : null,
              windSpeed: formValue.windSpeed ? parseFloat(formValue.windSpeed) : null,
              windDirection: formValue.windDirection || null
            }
          },
          photos: photoUrls,
          notes: formValue.notes,
          timestamp: new Date().toISOString(),
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        };

        const { data: insertedData, error } = await this.supabaseService.db
          .from('observations')
          .insert(observation)
          .select('id')
          .single();

        if (error) throw error;

        this.submitSuccess = true;

        // Navigate to dashboard and focus on the new observation
        setTimeout(() => {
          if (insertedData?.id) {
            // Import and use MapNavigationService
            import('../../../core/services/map-navigation.service').then(({ MapNavigationService }) => {
              const mapNavService = new MapNavigationService(this.router);
              mapNavService.navigateToNewObservation(
                this.selectedLocation.latitude,
                this.selectedLocation.longitude,
                insertedData.id,
                'blh_observation'
              );
            });
          } else {
            this.router.navigate(['/dashboard']);
          }
        }, 2000);

      } catch (error) {
        console.error('Error saving observation:', error);
        this.submitError = 'Failed to save observation. Please try again.';
      } finally {
        this.isSubmitting = false;
      }
    } else {
      this.markFormGroupTouched();
    }
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.blhForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  private markFormGroupTouched() {
    Object.keys(this.blhForm.controls).forEach(key => {
      const control = this.blhForm.get(key);
      control?.markAsTouched();
    });
  }

  goBack() {
    this.router.navigate(['/dashboard']);
  }
}
