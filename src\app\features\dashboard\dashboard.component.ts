import { Component, OnInit, OnDestroy, AfterViewInit, ElementRef, ViewChild, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router, NavigationEnd } from '@angular/router';
import { Map, NavigationControl, GeolocateControl, Marker, Popup } from 'maplibre-gl';
import { AuthService } from '../../core/services/auth.service';
import { GeolocationService } from '../../core/services/geolocation.service';
import { PredictionService } from '../../core/services/prediction.service';
import { SupabaseService } from '../../core/services/supabase.service';
import { environment } from '../../../environments/environment';
import { LoadingSpinnerComponent } from '../../shared/components/loading-spinner/loading-spinner.component';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-dashboard',
  standalone: true,
  imports: [CommonModule, RouterModule, LoadingSpinnerComponent],
  template: `
    <div class="dashboard-container">
      <!-- Mobile Header -->
      <header class="mobile-header">
        <div class="mobile-header-content">
          <div class="header-title">
            <h1>BCTV Dashboard</h1>
            <p *ngIf="currentUser" class="welcome-text">{{currentUser.firstName || currentUser.email}}</p>
          </div>
          <div class="header-actions">
            <button (click)="toggleMobileMenu()" class="mobile-menu-btn" [class.active]="showMobileMenu">
              <span class="hamburger-line"></span>
              <span class="hamburger-line"></span>
              <span class="hamburger-line"></span>
            </button>
          </div>
        </div>

        <!-- Mobile Navigation Menu -->
        <nav class="mobile-nav" [class.show]="showMobileMenu">
          <div class="mobile-nav-content">
            <a routerLink="/data-entry" class="mobile-nav-link" (click)="closeMobileMenu()">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <line x1="10" y1="9" x2="8" y2="9"/>
              </svg>
              Data Entry
            </a>
            <a routerLink="/data-browser" class="mobile-nav-link" (click)="closeMobileMenu()">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z"/>
                <path d="M8 21v-4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v4"/>
                <path d="M3 7a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2"/>
              </svg>
              Data Browser
            </a>
            <a routerLink="/predictions" class="mobile-nav-link" (click)="closeMobileMenu()">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
              </svg>
              Predictions
            </a>
            <button (click)="signOut()" class="mobile-nav-link logout-btn">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
              </svg>
              Sign Out
            </button>
          </div>
        </nav>
      </header>

      <!-- Desktop Header -->
      <header class="desktop-header">
        <div class="desktop-header-content">
          <div class="header-left">
            <h1>BCTV Management Dashboard</h1>
            <p *ngIf="currentUser">Welcome, {{currentUser.firstName || currentUser.email}}</p>
          </div>
          <nav class="desktop-nav">
            <a routerLink="/data-entry" class="nav-link">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <line x1="10" y1="9" x2="8" y2="9"/>
              </svg>
              Data Entry
            </a>
            <a routerLink="/data-browser" class="nav-link">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M3 7v10a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2z"/>
                <path d="M8 21v-4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v4"/>
                <path d="M3 7a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2"/>
              </svg>
              Data Browser
            </a>
            <a routerLink="/predictions" class="nav-link">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
              </svg>
              Predictions
            </a>
            <button (click)="signOut()" class="nav-link logout-btn">
              <svg class="nav-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
                <polyline points="16,17 21,12 16,7"/>
                <line x1="21" y1="12" x2="9" y2="12"/>
              </svg>
              Sign Out
            </button>
          </nav>
        </div>
      </header>

      <!-- Main Content -->
      <main class="dashboard-main">
        <!-- Mobile Quick Actions -->
        <section class="mobile-quick-actions">
          <h2>Quick Actions</h2>
          <div class="quick-actions-grid">
            <button routerLink="/data-entry/host-plant" class="quick-action-card">
              <div class="action-icon">🌿</div>
              <div class="action-content">
                <h3>Host Plant</h3>
                <p>Log plant observations</p>
              </div>
            </button>
            <button routerLink="/data-entry/blh" class="quick-action-card">
              <div class="action-icon">🦗</div>
              <div class="action-content">
                <h3>BLH Observation</h3>
                <p>Record leafhopper data</p>
              </div>
            </button>
            <button routerLink="/data-entry/bctv" class="quick-action-card">
              <div class="action-icon">🦠</div>
              <div class="action-content">
                <h3>BCTV Symptoms</h3>
                <p>Document virus symptoms</p>
              </div>
            </button>
            <button routerLink="/data-entry/eradication" class="quick-action-card">
              <div class="action-icon">🧹</div>
              <div class="action-content">
                <h3>Eradication</h3>
                <p>Log control efforts</p>
              </div>
            </button>
          </div>
        </section>

        <!-- Desktop Sidebar -->
        <aside class="desktop-sidebar">
          <div class="sidebar-section">
            <h3>Quick Actions</h3>
            <div class="action-buttons">
              <button routerLink="/data-entry/host-plant" class="action-btn">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                  <path d="M2 17l10 5 10-5"/>
                  <path d="M2 12l10 5 10-5"/>
                </svg>
                Log Host Plant
              </button>
              <button routerLink="/data-entry/blh" class="action-btn">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="3"/>
                  <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
                </svg>
                Log BLH Observation
              </button>
              <button routerLink="/data-entry/bctv" class="action-btn">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M12 2L2 7l10 5 10-5-10-5z"/>
                  <path d="M2 17l10 5 10-5"/>
                  <path d="M2 12l10 5 10-5"/>
                </svg>
                Log BCTV Symptoms
              </button>
              <button routerLink="/data-entry/eradication" class="action-btn">
                <svg class="action-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M3 6h18l-1.5 9A2 2 0 0 1 17.5 17H6.5a2 2 0 0 1-2-1.5L3 6z"/>
                  <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"/>
                </svg>
                Log Eradication
              </button>
            </div>
          </div>

          <div class="sidebar-section">
            <h3>📊 Risk Summary</h3>
            <div class="risk-summary" *ngIf="!loadingRisk; else riskLoading">
              <div class="risk-item" *ngFor="let risk of riskSummary">
                <div class="risk-indicator">
                  <div class="risk-level-badge" [class]="'risk-' + risk.level">
                    <span class="risk-icon">{{getRiskIcon(risk.level)}}</span>
                    <span class="risk-label">{{getRiskLevelDisplay(risk.level)}}</span>
                  </div>
                  <div class="risk-count">
                    <span class="count-number">{{risk.count}}</span>
                    <span class="count-label">{{risk.count === 1 ? 'area' : 'areas'}}</span>
                  </div>
                </div>
                <div class="risk-bar">
                  <div class="risk-bar-fill" [class]="'risk-' + risk.level" [style.width.%]="getRiskPercentage(risk)"></div>
                </div>
              </div>
              <div class="risk-summary-footer">
                <button (click)="viewFullRiskReport()" class="view-full-report-btn">
                  View Full Report →
                </button>
              </div>
            </div>
            <ng-template #riskLoading>
              <div class="loading-container">
                <app-loading-spinner size="small" message="Loading risk data..."></app-loading-spinner>
              </div>
            </ng-template>
          </div>

          <div class="sidebar-section">
            <h3>⚡ Recent Activity</h3>
            <div class="recent-activity" *ngIf="!loadingActivity; else activityLoading">
              <div class="activity-item" *ngFor="let activity of recentActivity; let i = index"
                   (click)="viewActivityDetails(activity)"
                   [class.clickable]="activity.id">
                <div class="activity-left">
                  <div class="activity-icon-wrapper" [class]="getActivityTypeClass(activity.type)">
                    <span class="activity-icon">{{getActivityIcon(activity.type)}}</span>
                  </div>
                  <div class="activity-line" *ngIf="i < recentActivity.length - 1"></div>
                </div>
                <div class="activity-content">
                  <div class="activity-header">
                    <div class="activity-title">{{getActivityTitle(activity)}}</div>
                    <div class="activity-time">{{getRelativeTime(activity.created_at || activity.time)}}</div>
                  </div>
                  <div class="activity-description" *ngIf="activity.description">
                    {{activity.description}}
                  </div>
                  <div class="activity-location" *ngIf="activity.latitude && activity.longitude">
                    📍 {{activity.latitude.toFixed(3)}}, {{activity.longitude.toFixed(3)}}
                  </div>
                </div>
              </div>
              <div class="activity-footer">
                <button routerLink="/data-browser" class="view-all-activity-btn">
                  View All Activity →
                </button>
              </div>
            </div>
            <ng-template #activityLoading>
              <div class="loading-container">
                <app-loading-spinner size="small" message="Loading activity..."></app-loading-spinner>
              </div>
            </ng-template>
          </div>
        </aside>

        <!-- Map Container -->
        <div class="map-container">
          <div class="map-controls">
            <button (click)="centerOnUserLocation()" class="map-control-btn" [disabled]="!userLocation">
              <svg class="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"/>
                <circle cx="12" cy="10" r="3"/>
              </svg>
              My Location
            </button>
            <button (click)="toggleHeatmap()" class="map-control-btn">
              <svg class="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" *ngIf="!showHeatmap">
                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"/>
                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"/>
              </svg>
              <svg class="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" *ngIf="showHeatmap">
                <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
                <polyline points="14,2 14,8 20,8"/>
              </svg>
              {{showHeatmap ? 'Normal View' : 'Heat Map'}}
            </button>
            <button (click)="refreshData()" class="map-control-btn" [disabled]="isRefreshing">
              <svg class="control-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" [class.spinning]="isRefreshing">
                <polyline points="23 4 23 10 17 10"/>
                <polyline points="1 20 1 14 7 14"/>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"/>
              </svg>
              {{isRefreshing ? 'Refreshing...' : 'Refresh'}}
            </button>
          </div>

          <div class="map-wrapper">
            <div #mapContainer class="map" [style.height.px]="mapHeight">
              <div class="map-legend">
                <h4>Legend</h4>
                <div class="legend-item">
                  <div class="legend-marker host-plant"></div>
                  <span>Host Plants</span>
                </div>
                <div class="legend-item">
                  <div class="legend-marker blh"></div>
                  <span>BLH Observations</span>
                </div>
                <div class="legend-item">
                  <div class="legend-marker bctv"></div>
                  <span>BCTV Symptoms</span>
                </div>
                <div class="legend-item">
                  <div class="legend-marker eradication"></div>
                  <span>Eradication Efforts</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  `,
  styles: [`
    /* Mobile-First Dashboard Styles */
    .dashboard-container {
      min-height: 100vh;
      background: var(--gray-50);
      display: flex;
      flex-direction: column;
    }

    /* Mobile Header */
    .mobile-header {
      display: block;
      background: white;
      border-bottom: 1px solid var(--gray-200);
      box-shadow: var(--shadow-sm);
      position: sticky;
      top: 0;
      z-index: 50;
    }

    .mobile-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: var(--space-4);
    }

    .header-title h1 {
      font-size: var(--text-xl);
      font-weight: 700;
      color: var(--gray-900);
      margin: 0;
    }

    .welcome-text {
      font-size: var(--text-sm);
      color: var(--gray-600);
      margin: var(--space-1) 0 0 0;
    }

    .mobile-menu-btn {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 44px;
      height: 44px;
      background: none;
      border: none;
      cursor: pointer;
      padding: var(--space-2);
      border-radius: var(--radius-lg);
      transition: background-color var(--transition-fast);
    }

    .mobile-menu-btn:hover {
      background: var(--gray-100);
    }

    .hamburger-line {
      width: 20px;
      height: 2px;
      background: var(--gray-700);
      margin: 2px 0;
      transition: all var(--transition-fast);
      border-radius: 1px;
    }

    .mobile-menu-btn.active .hamburger-line:nth-child(1) {
      transform: rotate(45deg) translate(5px, 5px);
    }

    .mobile-menu-btn.active .hamburger-line:nth-child(2) {
      opacity: 0;
    }

    .mobile-menu-btn.active .hamburger-line:nth-child(3) {
      transform: rotate(-45deg) translate(7px, -6px);
    }

    /* Mobile Navigation */
    .mobile-nav {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background: white;
      border-bottom: 1px solid var(--gray-200);
      box-shadow: var(--shadow-lg);
      transform: translateY(-100%);
      opacity: 0;
      visibility: hidden;
      transition: all var(--transition-normal);
    }

    .mobile-nav.show {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
    }

    .mobile-nav-content {
      padding: var(--space-4);
      display: flex;
      flex-direction: column;
      gap: var(--space-2);
    }

    .mobile-nav-link {
      display: flex;
      align-items: center;
      gap: var(--space-3);
      padding: var(--space-4);
      text-decoration: none;
      color: var(--gray-700);
      border-radius: var(--radius-lg);
      transition: all var(--transition-fast);
      font-weight: 500;
      min-height: 44px;
    }

    .mobile-nav-link:hover {
      background: var(--gray-100);
      color: var(--primary-600);
    }

    .mobile-nav-link.logout-btn {
      background: none;
      border: none;
      cursor: pointer;
      width: 100%;
      justify-content: flex-start;
      font-family: inherit;
      font-size: inherit;
    }

    .nav-icon {
      width: 20px;
      height: 20px;
      stroke-width: 2;
    }

    /* Desktop Header */
    .desktop-header {
      display: none;
    }

    /* Main Content Area */
    .dashboard-main {
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow: hidden;
    }

    /* Mobile Quick Actions */
    .mobile-quick-actions {
      display: block;
      padding: var(--space-6) var(--space-4);
    }

    .mobile-quick-actions h2 {
      font-size: var(--text-xl);
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: var(--space-6);
    }

    .quick-actions-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: var(--space-4);
    }

    .quick-action-card {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-xl);
      padding: var(--space-6);
      text-decoration: none;
      color: inherit;
      transition: all var(--transition-fast);
      box-shadow: var(--shadow-sm);
      cursor: pointer;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      min-height: 120px;
    }

    .quick-action-card:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      border-color: var(--primary-300);
    }

    .quick-action-card .action-icon {
      font-size: 2rem;
      margin-bottom: var(--space-3);
    }

    .quick-action-card h3 {
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-1) 0;
    }

    .quick-action-card p {
      font-size: var(--text-xs);
      color: var(--gray-600);
      margin: 0;
      line-height: 1.4;
    }

    /* Desktop Sidebar */
    .desktop-sidebar {
      display: none;
    }

    /* Map Container - Mobile First */
    .map-container {
      flex: 1;
      display: flex;
      flex-direction: column;
      background: white;
      margin: var(--space-4);
      border-radius: var(--radius-xl);
      box-shadow: var(--shadow-sm);
      overflow: hidden;
    }

    .map-controls {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-2);
      padding: var(--space-4);
      background: var(--gray-50);
      border-bottom: 1px solid var(--gray-200);
    }

    .map-control-btn {
      flex: 1;
      min-width: 120px;
      padding: var(--space-3) var(--space-4);
      background: var(--primary-600);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      min-height: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--space-2);
    }

    .map-control-btn:hover:not(:disabled) {
      background: var(--primary-700);
      transform: translateY(-1px);
    }

    .map-control-btn:disabled {
      background: var(--gray-400);
      cursor: not-allowed;
      transform: none;
    }

    .control-icon {
      width: 16px;
      height: 16px;
      stroke-width: 2;
    }

    .control-icon.spinning {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      from { transform: rotate(0deg); }
      to { transform: rotate(360deg); }
    }

    .map-wrapper {
      flex: 1;
      position: relative;
      min-height: 300px;
    }

    .map {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
    }

    .map-legend {
      position: absolute;
      bottom: var(--space-4);
      left: var(--space-4);
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(8px);
      padding: var(--space-3);
      border-radius: var(--radius-lg);
      box-shadow: var(--shadow-lg);
      border: 1px solid var(--gray-200);
      min-width: 120px;
      max-width: 160px;
      z-index: 1000;
      pointer-events: auto;
      transition: all var(--transition-fast);
    }

    .map-legend:hover {
      background: rgba(255, 255, 255, 0.98);
      transform: translateY(-1px);
      box-shadow: var(--shadow-xl);
    }

    .map-legend h4 {
      font-size: var(--text-xs);
      font-weight: 600;
      color: var(--gray-900);
      margin: 0 0 var(--space-2) 0;
      text-align: center;
    }

    .legend-item {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      margin-bottom: var(--space-1);
      font-size: var(--text-xs);
      color: var(--gray-700);
      padding: var(--space-1) 0;
      touch-action: manipulation;
    }

    .legend-item:last-child {
      margin-bottom: 0;
    }

    .legend-marker {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      flex-shrink: 0;
      border: 1px solid rgba(0, 0, 0, 0.1);
    }

    .legend-marker.host-plant { background: #28a745; }
    .legend-marker.blh { background: #ffc107; }
    .legend-marker.bctv { background: #dc3545; }
    .legend-marker.eradication { background: #007bff; }

    /* Map Popup Styles */
    :global(.maplibregl-popup-content) {
      padding: 0 !important;
      border-radius: var(--radius-lg) !important;
      box-shadow: var(--shadow-lg) !important;
    }

    :global(.observation-popup) {
      padding: var(--space-4);
      min-width: 250px;
    }

    :global(.observation-popup h4) {
      margin: 0 0 var(--space-3) 0;
      color: var(--gray-900);
      font-size: var(--text-base);
      font-weight: 600;
    }

    :global(.observation-popup p) {
      margin: 0 0 var(--space-2) 0;
      font-size: var(--text-sm);
      color: var(--gray-700);
      line-height: 1.4;
    }

    :global(.popup-details-btn) {
      width: 100%;
      padding: var(--space-2) var(--space-3);
      background: var(--primary-600);
      color: white;
      border: none;
      border-radius: var(--radius-md);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: background-color var(--transition-fast);
      margin-top: var(--space-3);
    }

    :global(.popup-details-btn:hover) {
      background: var(--primary-700);
    }

    /* Enhanced Risk Summary Styles */
    .risk-summary {
      display: flex;
      flex-direction: column;
      gap: var(--space-3);
    }

    .risk-item {
      background: var(--gray-50);
      border-radius: var(--radius-lg);
      padding: var(--space-3);
      border: 1px solid var(--gray-200);
      transition: all var(--transition-fast);
    }

    .risk-item:hover {
      background: white;
      box-shadow: var(--shadow-sm);
    }

    .risk-indicator {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--space-2);
    }

    .risk-level-badge {
      display: flex;
      align-items: center;
      gap: var(--space-2);
      padding: var(--space-1) var(--space-2);
      border-radius: var(--radius-md);
      font-size: var(--text-xs);
      font-weight: 500;
    }

    .risk-level-badge.risk-very_high {
      background: #fee2e2;
      color: var(--accent-red);
      border: 1px solid #fecaca;
    }

    .risk-level-badge.risk-high {
      background: #fed7aa;
      color: var(--accent-orange);
      border: 1px solid #fdba74;
    }

    .risk-level-badge.risk-moderate {
      background: #fef3c7;
      color: var(--accent-yellow);
      border: 1px solid #fde68a;
    }

    .risk-level-badge.risk-low {
      background: var(--primary-100);
      color: var(--primary-700);
      border: 1px solid var(--primary-200);
    }

    .risk-level-badge.risk-very_low {
      background: #dbeafe;
      color: var(--accent-blue);
      border: 1px solid #93c5fd;
    }

    .risk-icon {
      font-size: var(--text-base);
    }

    .risk-label {
      font-weight: 600;
    }

    .risk-count {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
      text-align: right;
    }

    .count-number {
      font-size: var(--text-lg);
      font-weight: 700;
      color: var(--gray-900);
      line-height: 1;
    }

    .count-label {
      font-size: var(--text-xs);
      color: var(--gray-600);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }

    .risk-bar {
      height: 4px;
      background: var(--gray-200);
      border-radius: 2px;
      overflow: hidden;
    }

    .risk-bar-fill {
      height: 100%;
      border-radius: 2px;
      transition: width var(--transition-normal);
    }

    .risk-bar-fill.risk-very_high { background: var(--accent-red); }
    .risk-bar-fill.risk-high { background: var(--accent-orange); }
    .risk-bar-fill.risk-moderate { background: var(--accent-yellow); }
    .risk-bar-fill.risk-low { background: var(--primary-600); }
    .risk-bar-fill.risk-very_low { background: var(--accent-blue); }

    .risk-summary-footer {
      margin-top: var(--space-2);
      padding-top: var(--space-4);
      border-top: 1px solid var(--gray-200);
    }

    .view-full-report-btn {
      width: 100%;
      padding: var(--space-3) var(--space-4);
      background: var(--primary-600);
      color: white;
      border: none;
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
    }

    .view-full-report-btn:hover {
      background: var(--primary-700);
      transform: translateY(-1px);
    }

    /* Enhanced Recent Activity Styles */
    .recent-activity {
      display: flex;
      flex-direction: column;
    }

    .activity-item {
      display: flex;
      gap: var(--space-4);
      padding: var(--space-4);
      border-radius: var(--radius-lg);
      transition: all var(--transition-fast);
      position: relative;
      cursor: pointer;
    }

    .activity-item.clickable:hover {
      background: var(--gray-50);
      transform: translateX(2px);
    }

    .activity-left {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;
    }

    .activity-icon-wrapper {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: var(--text-lg);
      font-weight: 600;
      flex-shrink: 0;
      border: 2px solid;
      background: white;
    }

    .activity-icon-wrapper.activity-host-plant {
      color: var(--primary-700);
      border-color: var(--primary-300);
      background: var(--primary-50);
    }

    .activity-icon-wrapper.activity-blh {
      color: var(--accent-yellow);
      border-color: #fde68a;
      background: #fef3c7;
    }

    .activity-icon-wrapper.activity-bctv {
      color: var(--accent-red);
      border-color: #fecaca;
      background: #fee2e2;
    }

    .activity-icon-wrapper.activity-eradication {
      color: var(--accent-blue);
      border-color: #93c5fd;
      background: #dbeafe;
    }

    .activity-icon-wrapper.activity-default {
      color: var(--gray-600);
      border-color: var(--gray-300);
      background: var(--gray-100);
    }

    .activity-line {
      width: 2px;
      flex: 1;
      background: var(--gray-200);
      margin-top: var(--space-2);
      min-height: 20px;
    }

    .activity-content {
      flex: 1;
      min-width: 0;
    }

    .activity-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--space-2);
      gap: var(--space-2);
    }

    .activity-title {
      font-size: var(--text-sm);
      font-weight: 600;
      color: var(--gray-900);
      line-height: 1.4;
    }

    .activity-time {
      font-size: var(--text-xs);
      color: var(--gray-500);
      white-space: nowrap;
      flex-shrink: 0;
    }

    .activity-description {
      font-size: var(--text-xs);
      color: var(--gray-600);
      line-height: 1.4;
      margin-bottom: var(--space-2);
    }

    .activity-location {
      font-size: var(--text-xs);
      color: var(--gray-500);
      font-family: var(--font-family-mono);
    }

    .activity-footer {
      padding: var(--space-4) var(--space-4) 0;
      border-top: 1px solid var(--gray-200);
      margin-top: var(--space-4);
    }

    .view-all-activity-btn {
      width: 100%;
      padding: var(--space-3) var(--space-4);
      background: var(--gray-100);
      color: var(--gray-700);
      border: 1px solid var(--gray-300);
      border-radius: var(--radius-lg);
      font-size: var(--text-sm);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-fast);
      text-decoration: none;
      display: block;
      text-align: center;
    }

    .view-all-activity-btn:hover {
      background: var(--gray-200);
      transform: translateY(-1px);
      color: var(--gray-800);
    }

    .loading-container {
      display: flex;
      justify-content: center;
      align-items: center;
      padding: var(--space-8);
    }

    /* Tablet Styles (768px and up) */
    @media (min-width: 768px) {
      .mobile-header {
        display: none;
      }

      .desktop-header {
        display: block;
        background: white;
        border-bottom: 1px solid var(--gray-200);
        box-shadow: var(--shadow-sm);
        position: sticky;
        top: 0;
        z-index: 50;
      }

      .desktop-header-content {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: var(--space-6) var(--space-8);
        max-width: 1400px;
        margin: 0 auto;
      }

      .header-left h1 {
        font-size: var(--text-2xl);
        font-weight: 700;
        color: var(--gray-900);
        margin: 0;
      }

      .header-left p {
        font-size: var(--text-sm);
        color: var(--gray-600);
        margin: var(--space-1) 0 0 0;
      }

      .desktop-nav {
        display: flex;
        gap: var(--space-6);
        align-items: center;
      }

      .desktop-nav .nav-link {
        display: flex;
        align-items: center;
        gap: var(--space-2);
        padding: var(--space-3) var(--space-4);
        text-decoration: none;
        color: var(--gray-700);
        border-radius: var(--radius-lg);
        transition: all var(--transition-fast);
        font-weight: 500;
        min-height: 44px;
      }

      .desktop-nav .nav-link:hover {
        background: var(--gray-100);
        color: var(--primary-600);
      }

      .desktop-nav .logout-btn {
        background: none;
        border: none;
        cursor: pointer;
        font-family: inherit;
        font-size: inherit;
      }

      .mobile-quick-actions {
        display: none;
      }

      .desktop-sidebar {
        display: block;
        width: 320px;
        background: white;
        border-right: 1px solid var(--gray-200);
        padding: var(--space-6);
        overflow-y: auto;
      }

      .sidebar-section {
        margin-bottom: var(--space-8);
      }

      .sidebar-section h3 {
        font-size: var(--text-lg);
        font-weight: 600;
        color: var(--gray-900);
        margin: 0 0 var(--space-4) 0;
      }

      .action-buttons {
        display: flex;
        flex-direction: column;
        gap: var(--space-3);
      }

      .action-btn {
        display: flex;
        align-items: center;
        gap: var(--space-3);
        padding: var(--space-4);
        background: var(--primary-600);
        color: white;
        border: none;
        border-radius: var(--radius-lg);
        text-decoration: none;
        font-size: var(--text-sm);
        font-weight: 500;
        cursor: pointer;
        transition: all var(--transition-fast);
        min-height: 44px;
      }

      .action-btn:hover {
        background: var(--primary-700);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
      }

      .action-icon {
        width: 20px;
        height: 20px;
        stroke-width: 2;
      }

      .dashboard-main {
        flex-direction: row;
      }

      .map-container {
        margin: var(--space-6);
      }

      .map {
        min-height: 500px;
      }

      .map-legend {
        bottom: var(--space-6);
        left: var(--space-6);
        min-width: 140px;
        max-width: 180px;
      }

      .quick-actions-grid {
        grid-template-columns: repeat(4, 1fr);
      }
    }

    /* Desktop Styles (1024px and up) */
    @media (min-width: 1024px) {
      .desktop-sidebar {
        width: 380px;
      }

      .map {
        min-height: 600px;
      }

      .map-legend {
        bottom: var(--space-8);
        left: var(--space-8);
        min-width: 160px;
        max-width: 200px;
        padding: var(--space-4);
      }

      .map-legend h4 {
        font-size: var(--text-sm);
        margin: 0 0 var(--space-3) 0;
      }

      .legend-item {
        margin-bottom: var(--space-2);
        padding: var(--space-1) 0;
      }

      .legend-marker {
        width: 12px;
        height: 12px;
      }

      .map-controls {
        padding: var(--space-6);
      }

      .map-control-btn {
        flex: none;
        min-width: 140px;
      }
    }

    /* Large Desktop Styles (1280px and up) */
    @media (min-width: 1280px) {
      .desktop-header-content {
        padding: var(--space-8) var(--space-12);
      }

      .desktop-sidebar {
        width: 420px;
        padding: var(--space-8);
      }

      .map-container {
        margin: var(--space-8);
      }
    }
  `]
})
export class DashboardComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('mapContainer', { static: true }) mapContainer!: ElementRef;

  map!: Map;
  mapHeight = 600;
  currentUser: any = null;
  userLocation: any = null;
  showHeatmap = false;
  showRiskOverlay = false;
  isRefreshing = false;
  loadingRisk = true;
  loadingActivity = true;
  showMobileMenu = false;

  riskSummary: any[] = [];
  recentActivity: any[] = [];

  private subscriptions: Subscription[] = [];

  constructor(
    private authService: AuthService,
    private geolocationService: GeolocationService,
    private predictionService: PredictionService,
    private supabaseService: SupabaseService,
    private router: Router
  ) {}

  ngOnInit() {
    this.currentUser = this.authService.currentUser;
    this.calculateMapHeight();
    this.getUserLocation();
    this.loadDashboardData();

    // Expose component instance globally for map popup navigation
    (window as any).dashboardComponent = this;

    // Listen for navigation events to refresh data when returning to dashboard
    this.subscriptions.push(
      this.router.events.pipe(
        filter(event => event instanceof NavigationEnd)
      ).subscribe((event: NavigationEnd) => {
        if (event.url === '/dashboard') {
          // Refresh data when navigating back to dashboard
          this.refreshData();
        }
      })
    );
  }

  @HostListener('window:focus', ['$event'])
  onWindowFocus(event: any): void {
    // Refresh data when window regains focus (user returns from another tab/app)
    this.refreshData();
  }

  ngAfterViewInit() {
    this.initializeMap();
  }

  ngOnDestroy() {
    this.subscriptions.forEach(sub => sub.unsubscribe());
    if (this.map) {
      this.map.remove();
    }
    // Clean up global reference
    if ((window as any).dashboardComponent === this) {
      delete (window as any).dashboardComponent;
    }
  }

  private calculateMapHeight() {
    // Calculate available height for map
    const headerHeight = 80;
    const controlsHeight = 60;
    const padding = 40;
    this.mapHeight = window.innerHeight - headerHeight - controlsHeight - padding;
  }

  private initializeMap() {
    try {
      console.log('🗺️ Initializing MapLibre GL map...');
      console.log('📍 Map container element:', this.mapContainer?.nativeElement);
      console.log('🎨 Map style URL:', environment.maplibre.style);

      this.map = new Map({
        container: this.mapContainer.nativeElement,
        style: environment.maplibre.style,
        center: [-119.4179, 36.7783], // Central California
        zoom: 4, // Wide view to show entire California and surrounding regions
        attributionControl: false
      });

      console.log('✅ Map instance created');

      // Add navigation controls
      this.map.addControl(new NavigationControl(), 'top-right');

      // Add geolocate control
      this.map.addControl(new GeolocateControl({
        positionOptions: {
          enableHighAccuracy: true
        },
        trackUserLocation: true
      }), 'top-right');

      console.log('🎛️ Map controls added');

      this.map.on('load', () => {
        console.log('🎉 Map loaded successfully, adding map features...');
        this.addCaliforniaBorders();
        this.addMajorCities();
        // Note: Terrain features (rivers) removed for cleaner BCTV-focused map
        // this.addTerrainFeatures();
        this.loadObservationData();
      });

      this.map.on('error', (e) => {
        console.error('❌ Map error:', e);
      });

    } catch (error) {
      console.error('❌ Error initializing map:', error);
    }
  }

  private getUserLocation() {
    this.geolocationService.getCurrentPosition().subscribe({
      next: (location) => {
        this.userLocation = location;
        if (this.map) {
          this.centerOnUserLocation();
        }
      },
      error: (error) => {
        console.warn('Could not get user location:', error);
      }
    });
  }

  private async loadDashboardData() {
    try {
      // Load both risk summary and recent activity data
      await Promise.all([
        this.loadRiskSummary(),
        this.loadRecentActivity()
      ]);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    }
  }

  private async loadRiskSummary() {
    try {
      this.loadingRisk = true;

      // For now, generate risk summary based on recent observations
      // In a real implementation, this would query a risk_assessments table
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString());

      if (error) throw error;

      // Generate mock risk summary based on observation count
      // This is a simplified approach - real implementation would use prediction service
      const observationCount = observations?.length || 0;

      if (observationCount === 0) {
        this.riskSummary = [
          { level: 'very_high', count: 0 },
          { level: 'high', count: 0 },
          { level: 'moderate', count: 0 },
          { level: 'low', count: 0 },
          { level: 'very_low', count: 0 }
        ];
      } else {
        // Generate risk distribution based on observation density
        this.riskSummary = [
          { level: 'very_high', count: Math.floor(observationCount * 0.1) },
          { level: 'high', count: Math.floor(observationCount * 0.2) },
          { level: 'moderate', count: Math.floor(observationCount * 0.3) },
          { level: 'low', count: Math.floor(observationCount * 0.25) },
          { level: 'very_low', count: Math.floor(observationCount * 0.15) }
        ];
      }

    } catch (error) {
      console.error('Error loading risk summary:', error);
      this.riskSummary = [];
    } finally {
      this.loadingRisk = false;
    }
  }

  private async loadRecentActivity() {
    try {
      this.loadingActivity = true;
      console.log('🔄 Loading recent activity...');

      // First check if we can connect to Supabase
      const { data: testConnection, error: connectionError } = await this.supabaseService.db
        .from('observations')
        .select('count', { count: 'exact', head: true });

      if (connectionError) {
        console.error('❌ Database connection error:', connectionError);

        // Check if it's a table not found error
        if (connectionError.message?.includes('relation "observations" does not exist')) {
          this.recentActivity = [{
            id: null,
            type: 'empty',
            created_at: new Date().toISOString(),
            latitude: null,
            longitude: null,
            description: 'Database table not found. Please set up the observations table first.',
            notes: null
          }];
        } else {
          this.recentActivity = [{
            id: null,
            type: 'empty',
            created_at: new Date().toISOString(),
            latitude: null,
            longitude: null,
            description: 'Unable to connect to database. Please check your Supabase configuration.',
            notes: null
          }];
        }
        this.loadingActivity = false;
        return;
      }

      // Load recent observations for activity feed
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('id, type, created_at, latitude, longitude, data, notes')
        .order('created_at', { ascending: false })
        .limit(8);

      if (error) {
        console.error('❌ Error loading observations:', error);
        this.recentActivity = [{
          id: null,
          type: 'empty',
          created_at: new Date().toISOString(),
          latitude: null,
          longitude: null,
          description: 'Error loading observations: ' + error.message,
          notes: null
        }];
        this.loadingActivity = false;
        return;
      }

      console.log('📊 Recent observations loaded:', observations?.length || 0);
      console.log('📋 Sample observation:', observations?.[0]);

      // Convert observations to activity items
      this.recentActivity = observations?.map(obs => {
        console.log('🔄 Processing observation:', obs.id, obs.type);
        return {
          id: obs.id,
          type: obs.type,
          created_at: obs.created_at,
          latitude: obs.latitude,
          longitude: obs.longitude,
          description: this.getActivityDescription(obs),
          notes: obs.notes
        };
      }) || [];

      console.log('✅ Recent activity processed:', this.recentActivity.length, 'items');

      // If no observations, show helpful empty state with sample data
      if (this.recentActivity.length === 0) {
        this.recentActivity = this.createSampleActivity();
      }

    } catch (error) {
      console.error('❌ Error loading recent activity:', error);
      this.recentActivity = [{
        id: null,
        type: 'empty',
        created_at: new Date().toISOString(),
        latitude: null,
        longitude: null,
        description: 'Unexpected error: ' + (error as Error).message,
        notes: null
      }];
    } finally {
      this.loadingActivity = false;
    }
  }

  private createSampleActivity() {
    // Create sample activity data for demonstration when no real data exists
    const now = new Date();
    return [
      {
        id: 'sample-1',
        type: 'host_plant',
        created_at: new Date(now.getTime() - 2 * 60 * 60 * 1000).toISOString(), // 2 hours ago
        latitude: 36.7783,
        longitude: -119.4179,
        description: 'Sugar beet observed with high density',
        notes: 'Sample data - no real observations yet'
      },
      {
        id: 'sample-2',
        type: 'blh_observation',
        created_at: new Date(now.getTime() - 6 * 60 * 60 * 1000).toISOString(), // 6 hours ago
        latitude: 36.7500,
        longitude: -119.4000,
        description: '15 adults, 8 nymphs recorded',
        notes: 'Sample data - no real observations yet'
      },
      {
        id: null,
        type: 'empty',
        created_at: new Date().toISOString(),
        latitude: null,
        longitude: null,
        description: 'Start adding real observations using the Data Entry section to see actual activity.',
        notes: null
      }
    ];
  }

  private async loadObservationData() {
    try {
      console.log('🗺️ Loading observation data for map...');

      // Load recent observations from Supabase
      const { data: observations, error } = await this.supabaseService.db
        .from('observations')
        .select('*')
        .gte('created_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
        .order('created_at', { ascending: false });

      if (error) throw error;

      console.log('📊 Observations retrieved:', observations?.length || 0);
      console.log('📋 Sample observation data:', observations?.[0]);

      // Clear existing markers first
      // Note: In a real implementation, you'd want to track markers to remove them

      // Add markers for each observation
      if (observations && observations.length > 0) {
        observations.forEach((obs, index) => {
          console.log(`📍 Adding marker ${index + 1}:`, {
            type: obs.type,
            lat: obs.latitude,
            lng: obs.longitude,
            created: obs.created_at
          });
          this.addObservationMarker(obs);
        });
        console.log(`✅ Added ${observations.length} markers to map`);
      } else {
        console.log('ℹ️ No observations found to display on map');
      }

    } catch (error) {
      console.error('❌ Error loading observation data:', error);
    }
  }

  private addObservationMarker(observation: any) {
    try {
      console.log('🎯 Creating marker for observation:', observation.type);

      // Validate coordinates
      const lat = parseFloat(observation.latitude);
      const lng = parseFloat(observation.longitude);

      if (isNaN(lat) || isNaN(lng)) {
        console.error('❌ Invalid coordinates:', { lat: observation.latitude, lng: observation.longitude });
        return;
      }

      if (lat < -90 || lat > 90 || lng < -180 || lng > 180) {
        console.error('❌ Coordinates out of range:', { lat, lng });
        return;
      }

      const color = this.getMarkerColor(observation.type);
      console.log('🎨 Marker color for type', observation.type, ':', color);

      const popup = new Popup({ offset: 25 }).setHTML(`
        <div class="observation-popup">
          <h4>${this.getObservationTitle(observation.type)}</h4>
          <p><strong>Date:</strong> ${new Date(observation.created_at).toLocaleDateString()}</p>
          <p><strong>Location:</strong> ${lat.toFixed(4)}, ${lng.toFixed(4)}</p>
          ${observation.notes ? `<p><strong>Notes:</strong> ${observation.notes.substring(0, 100)}${observation.notes.length > 100 ? '...' : ''}</p>` : ''}
          ${observation.photos && observation.photos.length > 0 ? `<p><strong>Photos:</strong> ${observation.photos.length} attached</p>` : ''}
          <button onclick="window.dashboardComponent.navigateToActivityDetails('${observation.id}')" class="popup-details-btn">
            View Full Details →
          </button>
        </div>
      `);

      if (!this.map) {
        console.error('❌ Map instance not available');
        return;
      }

      const marker = new Marker({ color })
        .setLngLat([lng, lat])
        .setPopup(popup)
        .addTo(this.map);

      console.log('✅ Marker added successfully at:', [lng, lat]);

    } catch (error) {
      console.error('❌ Error creating marker:', error);
    }
  }

  private getMarkerColor(type: string): string {
    switch (type) {
      case 'host_plant': return '#28a745';
      case 'blh_observation': return '#ffc107';
      case 'bctv_symptoms': return '#dc3545';
      case 'eradication_effort': return '#007bff';
      default: return '#6c757d';
    }
  }

  private getObservationTitle(type: string): string {
    switch (type) {
      case 'host_plant': return 'Host Plant Observation';
      case 'blh_observation': return 'BLH Observation';
      case 'bctv_symptoms': return 'BCTV Symptoms';
      case 'eradication_effort': return 'Eradication Effort';
      default: return 'Observation';
    }
  }

  getActivityIcon(type: string): string {
    switch (type) {
      case 'host_plant': return '🌿';
      case 'blh_observation': return '🦗';
      case 'bctv_symptoms': return '🦠';
      case 'eradication_effort': return '🧹';
      case 'empty': return '📝';
      case 'error': return '⚠️';
      default: return '📝';
    }
  }

  getActivityTitle(activity: any): string {
    if (!activity) return 'Unknown activity';

    if (activity.type === 'empty') {
      // Check if this is an error state or truly empty
      if (activity.description?.includes('Unable to load') || activity.description?.includes('Unable to connect')) {
        return 'Connection Issue';
      }
      return 'No Recent Activity';
    }

    switch (activity.type) {
      case 'host_plant': return 'Host plant logged';
      case 'blh_observation': return 'BLH observation recorded';
      case 'bctv_symptoms': return 'BCTV symptoms detected';
      case 'eradication_effort': return 'Eradication completed';
      default: return 'Observation recorded';
    }
  }

  getRelativeTime(dateString: string): string {
    if (!dateString) return 'Unknown time';

    try {
      const date = new Date(dateString);

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date string:', dateString);
        return 'Invalid date';
      }

      const now = new Date();
      const diffInMs = now.getTime() - date.getTime();
      const diffInMinutes = Math.floor(diffInMs / (1000 * 60));
      const diffInHours = Math.floor(diffInMinutes / 60);
      const diffInDays = Math.floor(diffInHours / 24);

      if (diffInMinutes < 1) {
        return 'Just now';
      } else if (diffInMinutes < 60) {
        return `${diffInMinutes} minute${diffInMinutes === 1 ? '' : 's'} ago`;
      } else if (diffInHours < 24) {
        return `${diffInHours} hour${diffInHours === 1 ? '' : 's'} ago`;
      } else if (diffInDays < 7) {
        return `${diffInDays} day${diffInDays === 1 ? '' : 's'} ago`;
      } else {
        return date.toLocaleDateString();
      }
    } catch (error) {
      console.error('Error parsing date:', dateString, error);
      return 'Invalid date';
    }
  }

  centerOnUserLocation() {
    if (this.userLocation && this.map) {
      this.map.flyTo({
        center: [this.userLocation.longitude, this.userLocation.latitude],
        zoom: 12,
        duration: 1000
      });
    }
  }

  // Enhanced map feature methods
  private addCaliforniaBorders() {
    if (!this.map) return;

    // Add California state border with accurate coordinates
    this.map.addSource('california-border', {
      type: 'geojson',
      data: {
        type: 'Feature',
        geometry: {
          type: 'Polygon',
          coordinates: [[
            // California border coordinates (simplified but accurate outline)
            [-124.409591, 42.009518], // Northwest corner (Oregon border)
            [-124.211606, 41.998294],
            [-124.053519, 41.995232],
            [-123.233256, 42.006186],
            [-122.378853, 42.011663],
            [-121.037003, 41.995232],
            [-120.001861, 41.995232],
            [-119.996384, 40.264519],
            [-120.001861, 39.239668],
            [-119.996384, 38.999346],
            [-119.558229, 38.899427],
            [-119.062917, 38.854558],
            [-118.71478, 38.101128],
            [-118.647906, 37.256566],
            [-118.314269, 36.843677],
            [-117.468706, 36.501861],
            [-116.540435, 36.501861],
            [-115.85034, 35.970598],
            [-114.634459, 35.00118],
            [-114.634459, 34.87521],
            [-114.470151, 34.710902],
            [-114.333228, 34.448009],
            [-114.136212, 34.305608],
            [-114.256551, 34.174162],
            [-114.415382, 34.108438],
            [-114.535721, 33.933176],
            [-114.497536, 33.697668],
            [-114.524921, 33.54264],
            [-114.727567, 33.40739],
            [-114.661844, 33.034958],
            [-114.524921, 33.029481],
            [-114.470151, 32.843265],
            [-114.524921, 32.755634],
            [-117.126467, 32.536853], // US-Mexico border start
            [-117.24057, 32.668003],
            [-117.252387, 32.876127],
            [-117.329114, 33.122589],
            [-117.471515, 33.297851],
            [-117.7837, 33.538836],
            [-118.183517, 33.763391],
            [-118.260244, 33.703145],
            [-118.413548, 33.741329],
            [-118.391641, 33.840068],
            [-118.566903, 34.042715],
            [-118.802411, 34.146334],
            [-119.218659, 34.146334],
            [-119.278905, 34.26667],
            [-119.558229, 34.415454],
            [-119.875891, 34.40998],
            [-120.138784, 34.475704],
            [-120.472421, 34.448319],
            [-120.64814, 34.579468],
            [-120.609955, 34.858792],
            [-120.670201, 35.099777],
            [-120.631016, 35.227347],
            [-120.894906, 35.264135],
            [-121.143372, 35.593014],
            [-121.312149, 35.684463],
            [-121.716543, 36.195153],
            [-121.896838, 36.315493],
            [-121.935023, 36.638785],
            [-121.858296, 36.6114],
            [-121.787112, 36.803894],
            [-121.929513, 36.978344],
            [-122.105263, 37.037003],
            [-122.312149, 37.195153],
            [-122.394338, 37.195153],
            [-122.400902, 37.309017],
            [-122.515291, 37.783843],
            [-122.515291, 37.853999],
            [-122.329114, 37.783843],
            [-122.406888, 38.15042],
            [-122.488077, 38.112235],
            [-122.504517, 38.166734],
            [-122.701533, 38.112235],
            [-122.718011, 38.639388],
            [-122.85715, 38.95082],
            [-123.029953, 39.277159],
            [-123.131116, 39.444438],
            [-123.41055, 39.444438],
            [-123.519527, 39.609016],
            [-123.707055, 39.719827],
            [-123.86123, 39.801461],
            [-124.109696, 40.105688],
            [-124.361116, 40.259855],
            [-124.410475, 40.439943],
            [-124.158531, 40.877937],
            [-124.109696, 41.025391],
            [-124.158531, 41.14083],
            [-124.065751, 41.442061],
            [-124.147985, 41.715296],
            [-124.257906, 41.781633],
            [-124.409591, 42.009518], // Close polygon back to start
          ]]
        },
        properties: {
          name: 'California'
        }
      }
    });

    this.map.addLayer({
      id: 'california-border',
      type: 'line',
      source: 'california-border',
      layout: {},
      paint: {
        'line-color': '#6b7280',
        'line-width': 1,
        'line-opacity': 0.4
      }
    });

    // Add California fill with very low opacity for subtle highlighting
    this.map.addLayer({
      id: 'california-fill',
      type: 'fill',
      source: 'california-border',
      layout: {},
      paint: {
        'fill-color': '#3b82f6',
        'fill-opacity': 0.05
      }
    });
  }

  private addMajorCities() {
    if (!this.map) return;

    const majorCities = [
      { name: 'Los Angeles', coordinates: [-118.2437, 34.0522] as [number, number] },
      { name: 'San Francisco', coordinates: [-122.4194, 37.7749] as [number, number] },
      { name: 'San Diego', coordinates: [-117.1611, 32.7157] as [number, number] },
      { name: 'Sacramento', coordinates: [-121.4944, 38.5816] as [number, number] },
      { name: 'Fresno', coordinates: [-119.7871, 36.7378] as [number, number] },
      { name: 'Oakland', coordinates: [-122.2711, 37.8044] as [number, number] },
      { name: 'Bakersfield', coordinates: [-119.0187, 35.3733] as [number, number] },
      { name: 'Anaheim', coordinates: [-117.9145, 33.8366] as [number, number] }
    ];

    majorCities.forEach(city => {
      new Marker({
        color: '#1f2937',
        scale: 0.8
      })
        .setLngLat(city.coordinates)
        .setPopup(new Popup({ offset: 25 }).setHTML(`
          <div>
            <h4>${city.name}</h4>
            <p>Major California City</p>
          </div>
        `))
        .addTo(this.map!);
    });
  }

  private addTerrainFeatures() {
    if (!this.map) return;

    // Add major rivers
    const rivers = [
      {
        name: 'Sacramento River',
        coordinates: [
          [-121.5, 40.5], [-121.4, 39.5], [-121.5, 38.5], [-121.6, 37.8]
        ]
      },
      {
        name: 'San Joaquin River',
        coordinates: [
          [-119.2, 37.1], [-120.5, 36.8], [-121.0, 36.5], [-121.3, 37.8]
        ]
      },
      {
        name: 'Colorado River',
        coordinates: [
          [-114.6, 32.7], [-114.5, 33.2], [-114.4, 33.7], [-114.3, 34.2]
        ]
      }
    ];

    rivers.forEach((river, index) => {
      this.map!.addSource(`river-${index}`, {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: river.coordinates
          },
          properties: { name: river.name }
        }
      });

      this.map!.addLayer({
        id: `river-${index}`,
        type: 'line',
        source: `river-${index}`,
        layout: {},
        paint: {
          'line-color': '#3b82f6',
          'line-width': 2,
          'line-opacity': 0.7
        }
      });
    });
  }

  toggleHeatmap() {
    this.showHeatmap = !this.showHeatmap;

    if (!this.map) return;

    if (this.showHeatmap) {
      // Add heatmap layer if it doesn't exist
      if (!this.map.getLayer('heatmap')) {
        this.addHeatmapLayer();
      } else {
        this.map.setLayoutProperty('heatmap', 'visibility', 'visible');
      }
    } else {
      // Hide heatmap layer
      if (this.map.getLayer('heatmap')) {
        this.map.setLayoutProperty('heatmap', 'visibility', 'none');
      }
    }

    console.log('Heatmap toggled:', this.showHeatmap);
  }

  private addHeatmapLayer() {
    if (!this.map) return;

    // Create heatmap source from observation data
    this.supabaseService.db
      .from('observations')
      .select('latitude, longitude, type')
      .then(({ data: observations }) => {
        if (!observations || !this.map) return;

        const heatmapData = {
          type: 'FeatureCollection' as const,
          features: observations.map(obs => ({
            type: 'Feature' as const,
            geometry: {
              type: 'Point' as const,
              coordinates: [obs.longitude, obs.latitude]
            },
            properties: {
              weight: this.getHeatmapWeight(obs.type)
            }
          }))
        };

        this.map!.addSource('heatmap-data', {
          type: 'geojson',
          data: heatmapData
        });

        this.map!.addLayer({
          id: 'heatmap',
          type: 'heatmap',
          source: 'heatmap-data',
          maxzoom: 15,
          paint: {
            'heatmap-weight': ['get', 'weight'],
            'heatmap-intensity': [
              'interpolate',
              ['linear'],
              ['zoom'],
              11, 1,
              15, 3
            ],
            'heatmap-color': [
              'interpolate',
              ['linear'],
              ['heatmap-density'],
              0, 'rgba(33,102,172,0)',
              0.2, 'rgb(103,169,207)',
              0.4, 'rgb(209,229,240)',
              0.6, 'rgb(253,219,199)',
              0.8, 'rgb(239,138,98)',
              1, 'rgb(178,24,43)'
            ],
            'heatmap-radius': [
              'interpolate',
              ['linear'],
              ['zoom'],
              11, 15,
              15, 20
            ],
            'heatmap-opacity': [
              'interpolate',
              ['linear'],
              ['zoom'],
              14, 1,
              15, 0
            ]
          }
        });
      });
  }

  private getHeatmapWeight(type: string): number {
    switch (type) {
      case 'bctv_symptoms': return 1.0;
      case 'blh_observation': return 0.8;
      case 'host_plant': return 0.6;
      case 'eradication_effort': return 0.4;
      default: return 0.5;
    }
  }

  refreshData() {
    this.isRefreshing = true;

    // Refresh all dashboard data
    Promise.all([
      this.loadDashboardData(),
      this.loadObservationData(),
      this.loadRecentActivity()
    ]).finally(() => {
      this.isRefreshing = false;
    });
  }

  // Method to refresh recent activity specifically
  refreshRecentActivity() {
    this.loadRecentActivity();
  }

  // Mobile menu methods
  toggleMobileMenu() {
    this.showMobileMenu = !this.showMobileMenu;
  }

  closeMobileMenu() {
    this.showMobileMenu = false;
  }

  // Risk level display methods
  getRiskLevelDisplay(level: string): string {
    const levelMap: { [key: string]: string } = {
      'very_low': 'Very Low',
      'low': 'Low',
      'moderate': 'Moderate',
      'high': 'High',
      'very_high': 'Very High'
    };
    return levelMap[level] || level;
  }

  getRiskIcon(level: string): string {
    const iconMap: { [key: string]: string } = {
      'very_high': '🔴',
      'high': '🟠',
      'moderate': '🟡',
      'low': '🟢',
      'very_low': '🔵'
    };
    return iconMap[level] || '⚪';
  }

  getRiskPercentage(risk: any): number {
    // Calculate percentage based on total areas
    const total = this.riskSummary.reduce((sum, r) => sum + r.count, 0);
    return total > 0 ? (risk.count / total) * 100 : 0;
  }

  viewFullRiskReport() {
    this.router.navigate(['/predictions']);
  }

  getActivityTypeClass(type: string): string {
    const classMap: { [key: string]: string } = {
      'host_plant': 'activity-host-plant',
      'blh_observation': 'activity-blh',
      'bctv_symptoms': 'activity-bctv',
      'eradication_effort': 'activity-eradication'
    };
    return classMap[type] || 'activity-default';
  }

  getActivityDescription(observation: any): string {
    if (!observation.data) return '';

    switch (observation.type) {
      case 'host_plant':
        return `${observation.data.species || 'Unknown species'} observed with ${observation.data.density || 'unknown'} density`;
      case 'blh_observation':
        const adults = observation.data.adultCount || 0;
        const nymphs = observation.data.nymphCount || 0;
        return `${adults} adults, ${nymphs} nymphs recorded`;
      case 'bctv_symptoms':
        return `${observation.data.severity || 'Unknown'} severity symptoms on ${observation.data.affectedPlantCount || 0} plants`;
      case 'eradication_effort':
        return `${observation.data.method || 'Control method'} applied to ${observation.data.areaSize || 0} m² area`;
      default:
        return 'Field observation recorded';
    }
  }

  // Activity methods
  viewActivityDetails(activity: any) {
    if (activity.id) {
      // Navigate to activity details page
      this.router.navigate(['/activity-details', activity.id]);
    } else {
      console.warn('Activity has no ID, cannot navigate to details');
    }
  }

  // Global navigation method for map popups
  navigateToActivityDetails(activityId: string) {
    console.log('🔄 Navigating to activity details:', activityId);
    this.router.navigate(['/activity-details', activityId]);
  }



  // Map control methods
  toggleRiskOverlay() {
    this.showRiskOverlay = !this.showRiskOverlay;

    if (!this.map) return;

    if (this.showRiskOverlay) {
      // Add risk overlay layer if it doesn't exist
      if (!this.map.getLayer('risk-overlay')) {
        this.addRiskOverlayLayer();
      } else {
        this.map.setLayoutProperty('risk-overlay', 'visibility', 'visible');
      }
    } else {
      // Hide risk overlay layer
      if (this.map.getLayer('risk-overlay')) {
        this.map.setLayoutProperty('risk-overlay', 'visibility', 'none');
      }
    }

    console.log('Risk overlay toggled:', this.showRiskOverlay);
  }

  private addRiskOverlayLayer() {
    if (!this.map) return;

    // Create risk zones based on observation density and historical data
    const riskZones = [
      {
        name: 'Central Valley High Risk',
        level: 'high',
        coordinates: [
          [-121.5, 37.0], [-121.0, 37.0], [-121.0, 36.0], [-121.5, 36.0], [-121.5, 37.0]
        ]
      },
      {
        name: 'Imperial Valley Moderate Risk',
        level: 'moderate',
        coordinates: [
          [-116.0, 33.0], [-115.0, 33.0], [-115.0, 32.5], [-116.0, 32.5], [-116.0, 33.0]
        ]
      },
      {
        name: 'San Joaquin Valley High Risk',
        level: 'high',
        coordinates: [
          [-120.5, 37.5], [-119.5, 37.5], [-119.5, 36.5], [-120.5, 36.5], [-120.5, 37.5]
        ]
      },
      {
        name: 'Sacramento Valley Moderate Risk',
        level: 'moderate',
        coordinates: [
          [-122.0, 39.0], [-121.0, 39.0], [-121.0, 38.0], [-122.0, 38.0], [-122.0, 39.0]
        ]
      }
    ];

    riskZones.forEach((zone, index) => {
      this.map!.addSource(`risk-zone-${index}`, {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Polygon',
            coordinates: [zone.coordinates]
          },
          properties: {
            name: zone.name,
            level: zone.level
          }
        }
      });

      const color = this.getRiskZoneColor(zone.level);

      this.map!.addLayer({
        id: `risk-zone-${index}`,
        type: 'fill',
        source: `risk-zone-${index}`,
        layout: {},
        paint: {
          'fill-color': color,
          'fill-opacity': 0.3
        }
      });

      this.map!.addLayer({
        id: `risk-zone-border-${index}`,
        type: 'line',
        source: `risk-zone-${index}`,
        layout: {},
        paint: {
          'line-color': color,
          'line-width': 2,
          'line-opacity': 0.8
        }
      });
    });

    // Group all risk layers under 'risk-overlay' for easy toggling
    this.map.addLayer({
      id: 'risk-overlay',
      type: 'background',
      paint: {
        'background-opacity': 0
      }
    });
  }

  private getRiskZoneColor(level: string): string {
    switch (level) {
      case 'very_high': return '#dc2626';
      case 'high': return '#ea580c';
      case 'moderate': return '#ca8a04';
      case 'low': return '#16a34a';
      case 'very_low': return '#059669';
      default: return '#6b7280';
    }
  }

  signOut() {
    this.authService.signOut().subscribe();
  }
}
